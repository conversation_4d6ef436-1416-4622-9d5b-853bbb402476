#!/usr/bin/env python3

import pandas as pd

def check_allstate():
    df_expected = pd.read_excel('Bilxy ********.xlsx')
    allstate_records = df_expected[df_expected['Insurance'] == 'ALLSTATE MISSISSIPPI CLAI']
    print(f'ALLSTATE records: {len(allstate_records)}')
    print('Sample ALLSTATE records:')
    for i, row in allstate_records.head(10).iterrows():
        account = row['Account No']
        patient = row['Patient Name']
        dos = row['DOS']
        amount = row['Claim Amount']
        print(f'  Account: {account}, Patient: {patient}, DOS: {dos}, Amount: {amount}')

if __name__ == '__main__':
    check_allstate()
