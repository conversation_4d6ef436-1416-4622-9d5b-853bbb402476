#!/usr/bin/env python3

import sys
sys.path.append('.')
from main import extract_text_from_pdf, parse_insurance_claims, create_xlsx_file

def test_parsing():
    # Test the updated parsing
    text_content = extract_text_from_pdf('Biloxi 08132025.pdf')
    print('Text extracted, length:', len(text_content))

    claims_data = parse_insurance_claims(text_content)
    print('Claims parsed, count:', len(claims_data))

    if claims_data:
        print('First few claims:')
        for i, claim in enumerate(claims_data[:5]):
            print(f'{i+1}: Keys: {list(claim.keys())}')
            print(f'    Values: {claim}')
        
        # Create test output file
        create_xlsx_file(claims_data, 'test_output_improved.xlsx')
        print('Test file created: test_output_improved.xlsx')
        
        # Compare with expected
        import pandas as pd
        try:
            df_expected = pd.read_excel('Bilxy 08142025.xlsx')
            print(f'Expected records: {len(df_expected)}')
            print(f'Our records: {len(claims_data)}')
            print(f'Difference: {len(df_expected) - len(claims_data)}')
        except Exception as e:
            print(f'Could not compare with expected: {e}')

if __name__ == '__main__':
    test_parsing()
