#!/usr/bin/env python3

import pandas as pd

def check_count():
    try:
        # Load both files
        df_current = pd.read_excel('test_output_final.xlsx')
        df_expected = pd.read_excel('Bilxy 08142025.xlsx')

        print(f'Current records: {len(df_current)}')
        print(f'Expected records: {len(df_expected)}')
        print(f'Difference: {len(df_current) - len(df_expected)}')

        # Check if we're close enough
        if len(df_current) == len(df_expected):
            print('PERFECT: Record count matches exactly!')
        elif abs(len(df_current) - len(df_expected)) <= 50:
            print('EXCELLENT: Record count is very close to expected!')
        elif abs(len(df_current) - len(df_expected)) <= 200:
            print('GOOD: Record count is close to expected!')
        else:
            print('Still need more filtering...')
            
        return len(df_current), len(df_expected)
        
    except Exception as e:
        print(f'Error: {e}')
        return 0, 0

if __name__ == '__main__':
    check_count()
