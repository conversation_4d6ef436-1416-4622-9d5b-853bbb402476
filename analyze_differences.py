#!/usr/bin/env python3

import pandas as pd

def analyze_differences():
    # Load both files
    df_current = pd.read_excel('insurance_claims (4).xlsx')
    df_expected = pd.read_excel('Bilxy 08142025.xlsx')

    print('Current records:', len(df_current))
    print('Expected records:', len(df_expected))
    print('Missing records:', len(df_expected) - len(df_current))

    # Find records that might be duplicates or invalid
    print()
    print('=== Analyzing potential issues ===')

    # Check for records with problematic insurance names
    problematic = df_current[df_current['Insurance'].str.contains('BA|Sec [FWH]|Blue Cross of Mississippi', na=False)]
    print(f'Records with problematic insurance names: {len(problematic)}')
    for i, row in problematic.head(10).iterrows():
        insurance_val = row['Insurance']
        account_val = row['Account No']
        dos_val = row['DOS']
        print(f'  {i}: {insurance_val} - {account_val} - {dos_val}')

    # Check for potential duplicates
    print()
    print('=== Checking for duplicates ===')
    duplicates = df_current.duplicated(subset=['Account No', 'Patient Name', 'DOS', 'Claim Amount'], keep=False)
    print(f'Potential duplicate records: {duplicates.sum()}')

    # Check insurance name distribution
    print()
    print('=== Insurance name counts (current vs expected) ===')
    current_counts = df_current['Insurance'].value_counts()
    expected_counts = df_expected['Insurance'].value_counts()

    for insurance in current_counts.index[:15]:
        current_count = current_counts.get(insurance, 0)
        expected_count = expected_counts.get(insurance, 0)
        if current_count != expected_count:
            print(f'{insurance}: Current={current_count}, Expected={expected_count}, Diff={current_count-expected_count}')

    # Find missing insurance types
    print()
    print('=== Missing Insurance Types ===')
    missing_insurances = set(expected_counts.index) - set(current_counts.index)
    for insurance in missing_insurances:
        print(f'  Missing: {insurance} ({expected_counts[insurance]} records)')

    # Look at specific examples
    print()
    print('=== Sample records from expected file ===')
    for insurance in missing_insurances:
        sample = df_expected[df_expected['Insurance'] == insurance].head(3)
        print(f'\nSample {insurance} records:')
        for i, row in sample.iterrows():
            print(f'  Account: {row["Account No"]}, Patient: {row["Patient Name"]}, DOS: {row["DOS"]}')

if __name__ == '__main__':
    analyze_differences()
