#!/usr/bin/env python3

import pandas as pd

def analyze_differences():
    # Load both files
    df_current = pd.read_excel('test_output_fixed.xlsx')
    df_expected = pd.read_excel('Bilxy 08142025.xlsx')

    print('Current records:', len(df_current))
    print('Expected records:', len(df_expected))
    print('Extra records:', len(df_current) - len(df_expected))

    # Find records that might be duplicates or invalid
    print()
    print('=== Analyzing potential issues ===')

    # Check for records with problematic insurance names
    problematic = df_current[df_current['Insurance'].str.contains('BA|Sec [FWH]|Blue Cross of Mississippi', na=False)]
    print(f'Records with problematic insurance names: {len(problematic)}')
    for i, row in problematic.head(10).iterrows():
        insurance_val = row['Insurance']
        account_val = row['Account No']
        dos_val = row['DOS']
        print(f'  {i}: {insurance_val} - {account_val} - {dos_val}')

    # Check for potential duplicates
    print()
    print('=== Checking for duplicates ===')
    duplicates = df_current.duplicated(subset=['Account No', 'Patient Name', 'DOS', 'Claim Amount'], keep=False)
    print(f'Potential duplicate records: {duplicates.sum()}')

    # Check insurance name distribution
    print()
    print('=== Insurance name counts (current vs expected) ===')
    current_counts = df_current['Insurance'].value_counts()
    expected_counts = df_expected['Insurance'].value_counts()

    for insurance in current_counts.index[:15]:
        current_count = current_counts.get(insurance, 0)
        expected_count = expected_counts.get(insurance, 0)
        if current_count != expected_count:
            print(f'{insurance}: Current={current_count}, Expected={expected_count}, Diff={current_count-expected_count}')

    # Find records that exist in current but not in expected
    print()
    print('=== Records in current but not in expected ===')
    
    # Create a key for comparison
    df_current['key'] = df_current['Account No'].astype(str) + '_' + df_current['DOS'].astype(str) + '_' + df_current['Claim Amount'].astype(str)
    df_expected['key'] = df_expected['Account No'].astype(str) + '_' + df_expected['DOS'].astype(str) + '_' + df_expected['Claim Amount'].astype(str)
    
    current_keys = set(df_current['key'])
    expected_keys = set(df_expected['key'])
    
    extra_keys = current_keys - expected_keys
    print(f'Records in current but not expected: {len(extra_keys)}')
    
    if extra_keys:
        extra_records = df_current[df_current['key'].isin(extra_keys)]
        print('Extra records:')
        for i, row in extra_records.iterrows():
            insurance_val = row['Insurance']
            account_val = row['Account No']
            dos_val = row['DOS']
            amount_val = row['Claim Amount']
            print(f'  {insurance_val} - {account_val} - {dos_val} - ${amount_val}')

if __name__ == '__main__':
    analyze_differences()
