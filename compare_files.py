#!/usr/bin/env python3

import pandas as pd

def compare_files():
    print('=== Current output (test_output_final.xlsx) ===')
    try:
        df_current = pd.read_excel('test_output_final.xlsx')
        print('Shape:', df_current.shape)
        print('Columns:', df_current.columns.tolist())
        print('First few rows:')
        print(df_current.head())
        print()
        print('Sample insurance values:', df_current['Insurance'].unique()[:10])
        print()
    except Exception as e:
        print(f'Error reading current file: {e}')
        return

    print('=== Expected output (Bilxy 08142025.xlsx) ===')
    try:
        df_expected = pd.read_excel('Bilxy 08142025.xlsx')
        print('Shape:', df_expected.shape)
        print('Columns:', df_expected.columns.tolist())
        print('First few rows:')
        print(df_expected.head())
        print()
        print('Sample insurance values:', df_expected['Insurance'].unique()[:10])
        print()
    except Exception as e:
        print(f'Error reading expected file: {e}')
        return

    print('=== Comparison ===')
    try:
        print(f'Current records: {len(df_current)}')
        print(f'Expected records: {len(df_expected)}')
        print(f'Difference: {len(df_current) - len(df_expected)}')
        
        # Compare some specific records
        print()
        print('Current first 5 insurance values:')
        for i in range(min(5, len(df_current))):
            insurance_val = df_current.iloc[i]['Insurance']
            print(f'{i+1}: {insurance_val}')
        
        print()
        print('Expected first 5 insurance values:')
        for i in range(min(5, len(df_expected))):
            insurance_val = df_expected.iloc[i]['Insurance']
            print(f'{i+1}: {insurance_val}')
            
        # Check for differences in insurance names
        print()
        print('=== Insurance name differences ===')
        current_insurance = set(df_current['Insurance'].unique())
        expected_insurance = set(df_expected['Insurance'].unique())
        
        only_in_current = current_insurance - expected_insurance
        only_in_expected = expected_insurance - current_insurance
        
        if only_in_current:
            print('Insurance names only in current output:')
            for ins in sorted(only_in_current):
                print(f'  - {ins}')
        
        if only_in_expected:
            print('Insurance names only in expected output:')
            for ins in sorted(only_in_expected):
                print(f'  - {ins}')
                
    except Exception as e:
        print(f'Error in comparison: {e}')

if __name__ == '__main__':
    compare_files()
