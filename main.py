from fastapi import Fast<PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import os
import tempfile
import pandas as pd
import re
from datetime import datetime
import PyPDF2
import pdfplumber
import openpyxl
from openpyxl.styles import Font, Alignment

app = FastAPI()

def extract_text_from_pdf(pdf_path):
    text_content = ""
    try:
        # Use PyPDF2 first (faster)
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"
        
        # If no text found, try pdfplumber (slower but better)
        if not text_content.strip():
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    text_content += page.extract_text() + "\n"
    except Exception as e:
        pass
    return text_content

def parse_insurance_claims(text_content):
    claims_data = []
    lines = [line.strip() for line in text_content.split('\n') if line.strip()]
    data_started = False
    current_account = ""
    current_patient = ""
    
    # Comprehensive pattern to catch all formats from PDF
    patterns = [
        # Pattern 1: Pri/Sec/Oth E/W/P/F amount overdue insurance_id
        re.compile(r'(.*)\s+(Pri|Sec|Oth)\s+[EWPF]\s+([\d,]+\.?\d*)\s+(\d+)(?:\s+([\w\-]+))?\s*$'),
        # Pattern 2: Pri/Sec/Oth E/W/P/F amount WtERA/Forwd overdue insurance_id  
        re.compile(r'(.*)\s+(Pri|Sec|Oth)\s+[EWPF]\s+([\d,]+\.?\d*)\s+(WtERA\d+|Forwd)\s+(\d+)(?:\s+([\w\-]+))?\s*$'),
        # Pattern 3: Any line ending with decimal amount + number + alphanumeric
        re.compile(r'(.*)\s+([\d,]+\.\d{2})\s+(\d+)\s+([A-Za-z0-9\-]+)\s*$'),
        # Pattern 4: Lines with just amount and insurance ID (no overdue)
        re.compile(r'(.*)\s+([\d,]+\.\d{2})\s+([A-Za-z0-9\-]+)\s*$')
    ]
    # Account pattern for new account lines
    account_pattern = re.compile(r'^([A-Z]+\d+X?)\s+(.+?)\s+\d{2}/\d{2}/\d{2}')

    unmatched_lines = []
    for line in lines:
        if not data_started:
            if re.search(r'A\s*c\s*c\s*o\s*u\s*n\s*t.*P\s*a\s*t\s*i\s*e\s*n\s*t.*D\s*a\s*t\s*e\s*F\s*i\s*l\s*e\s*d.*F\s*r\s*o\s*m.*T\s*o.*I\s*n\s*s\s*u\s*r\s*a\s*n\s*c\s*e\s*C\s*o\s*m\s*p\s*a\s*n\s*y', line, re.IGNORECASE):
                data_started = True
                continue
            elif any(keyword in line.upper() for keyword in ["OVERDUE", "UNPAID", "INSURANCE", "TIME:", "SYSTEM:", "PAGE:"]):
                continue

        if not data_started:
            continue

        # Check if this is a new account line
        account_match = account_pattern.match(line)
        if account_match:
            current_account = account_match.group(1)
            current_patient = account_match.group(2).strip()
        
        # Enhanced splitting: split on every date pattern and on multiple spaces (4+)
        # This helps catch concatenated records and poorly formatted lines
        split_indices = [m.start() for m in re.finditer(r'\d{2}/\d{2}/\d{2}', line)]
        segments = []
        if len(split_indices) > 1:
            last_pos = 0
            for pos in split_indices[1:]:
                segments.append(line[last_pos:pos].strip())
                last_pos = pos
            segments.append(line[last_pos:].strip())
        else:
            segments = [line]

        # Further split each segment on 4+ spaces (common in PDF tables)
        lines_to_process = []
        for seg in segments:
            # Only split if segment is long enough
            if len(seg) > 30 and re.search(r' {4,}', seg):
                parts = [s.strip() for s in re.split(r' {4,}', seg) if s.strip()]
                lines_to_process.extend(parts)
            else:
                lines_to_process.append(seg)

        # Process each split line
        for process_line in lines_to_process:
            # Try to match any line with insurance claims using multiple patterns
            matched = False
            for i, pattern in enumerate(patterns):
                match = pattern.match(process_line)
                if match and current_account and current_patient:
                    matched = True
                    if i == 0:  # Pattern 1: Pri/Sec/Oth E/W/P/F amount overdue insurance_id
                        prefix_part = match.group(1).strip()
                        claim_amount = match.group(3).replace(',', '')
                        over_due = match.group(4)
                        insurance_id = match.group(5).strip() if match.group(5) else "N/A"
                    elif i == 1:  # Pattern 2: Pri/Sec/Oth E/W/P/F amount WtERA/Forwd overdue insurance_id
                        prefix_part = match.group(1).strip()
                        claim_amount = match.group(3).replace(',', '')
                        over_due = match.group(5)
                        insurance_id = match.group(6).strip() if match.group(6) else "N/A"
                    elif i == 2:  # Pattern 3: amount overdue insurance_id
                        prefix_part = match.group(1).strip()
                        claim_amount = match.group(2).replace(',', '')
                        over_due = match.group(3)
                        insurance_id = match.group(4).strip()
                    elif i == 3:  # Pattern 4: amount insurance_id (no overdue)
                        prefix_part = match.group(1).strip()
                        claim_amount = match.group(2).replace(',', '')
                        over_due = "0.00"
                        insurance_id = match.group(3).strip()
                    break

            # Fallback: if not matched, try to extract claim-like data from the line
            if not matched and current_account and current_patient:
                # Look for a monetary value and an insurance id-like string
                fallback = re.search(r'(.*?)([\d,]+\.\d{2})\s+(\d+)?\s*([A-Za-z0-9\-]+)?\s*$', process_line)
                if fallback:
                    prefix_part = fallback.group(1).strip()
                    claim_amount = fallback.group(2).replace(',', '')
                    over_due = fallback.group(3) if fallback.group(3) else "0.00"
                    insurance_id = fallback.group(4).strip() if fallback.group(4) else "N/A"
                    matched = True

            if matched:
                # Skip only obvious header/footer lines
                if any(skip_word in process_line.upper() for skip_word in ['PAGE:', 'TIME:', 'SYSTEM:', 'RUN:', 'REPORT DATE']):
                    print(f"SKIPPED (header/footer): {process_line}")
                    continue
                # Skip if line contains header text patterns
                if re.search(r'Account\s+Patient\s+Name|Claim\s+Last\s+Dates|Over\s+Due\s+Insurance', process_line, re.IGNORECASE):
                    print(f"SKIPPED (header pattern): {process_line}")
                    continue
                # Extract dates and insurance from prefix
                dates = re.findall(r'\d{2}/\d{2}/\d{2}', prefix_part)
                if dates:
                    date_from = dates[-1]  # Use last date as DOS
                    # Remove dates from prefix to get insurance name
                    insurance_part = re.sub(r'\d{2}/\d{2}/\d{2}', '', prefix_part).strip()
                    insurance = insurance_part if insurance_part else "Unknown"
                else:
                    date_from = "N/A"
                    insurance = prefix_part
                
                # Clean insurance name - be more inclusive
                # Clean up insurance name
                insurance = re.sub(r'^(\d+\s+)?', '', insurance)  # Remove leading numbers
                insurance = re.sub(r'\s*\$[\d,]+.*$', '', insurance)  # Remove $ amounts
                insurance = re.sub(r'\s+BA\$.*$', '', insurance)  # Remove BA$ patterns
                
                # Only skip obvious processing text, keep everything else
                skip_patterns = ['claim filed to', 'checked.*web portal', 'check# found and claim']
                if any(re.search(pattern, insurance.lower()) for pattern in skip_patterns):
                    print(f"SKIPPED (processing text): {process_line}")
                    continue
                
                # Keep any text that has letters, even if mixed with other characters
                if re.search(r'[A-Za-z]', insurance) and insurance.strip():
                    insurance = insurance.strip()
                else:
                    insurance = "Unknown"
                try:
                    dos_date = datetime.strptime(date_from, '%m/%d/%y')
                    dos = dos_date.strftime('%d-%m-%Y')
                except:
                    dos = date_from
                try:
                    claim_amount = float(claim_amount)
                except:
                    claim_amount = 0.0
                try:
                    over_due = float(over_due)
                except:
                    over_due = 0.0
                print(f"EXTRACTED: {process_line} -> Insurance: {insurance}")
                claims_data.append({
                    'Account No': current_account,
                    'Patient Name': current_patient,
                    'Insurance': insurance,
                    'DOS': dos,
                    'Insurance ID': insurance_id,
                    'Claim Amount': claim_amount,
                    'Over Due': over_due
                })
            else:
                print(f"NOT MATCHED: {process_line}")
                # If not matched and not a header/footer, log for debug
                if not any(skip_word in process_line.upper() for skip_word in ['PAGE:', 'TIME:', 'SYSTEM:', 'RUN:', 'REPORT DATE']) and not re.search(r'Account\s+Patient\s+Name|Claim\s+Last\s+Dates|Over\s+Due\s+Insurance', process_line, re.IGNORECASE):
                    unmatched_lines.append(process_line)


    # Write unmatched lines to debug file for review
    if unmatched_lines:
        with open('debug_text.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(unmatched_lines))
    return claims_data

def create_xlsx_file(claims_data, output_path):
    df = pd.DataFrame(claims_data)
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Insurance Claims', index=False)
        workbook = writer.book
        worksheet = writer.sheets['Insurance Claims']
        
        header_font = Font(bold=True)
        header_alignment = Alignment(horizontal='center')
        
        for col_num, column_title in enumerate(df.columns, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.font = header_font
            cell.alignment = header_alignment
        
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

@app.get("/", response_class=HTMLResponse)
async def read_root():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>PDF to Excel Converter</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
            .upload-area:hover { border-color: #007bff; }
            button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .result { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        </style>
    </head>
    <body>
        <h1>Insurance Claims PDF to Excel Converter</h1>
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-area">
                <input type="file" id="pdfFile" name="file" accept=".pdf" required>
                <p>Select a PDF file to convert</p>
            </div>
            <button type="submit">Convert to Excel</button>
        </form>
        <div id="result" class="result" style="display:none;"></div>
        
        <script>
            document.getElementById('uploadForm').onsubmit = async function(e) {
                e.preventDefault();
                const formData = new FormData();
                const fileInput = document.getElementById('pdfFile');
                formData.append('file', fileInput.files[0]);
                
                document.getElementById('result').innerHTML = 'Processing...';
                document.getElementById('result').style.display = 'block';
                
                try {
                    const response = await fetch('/upload/', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'insurance_claims.xlsx';
                        a.click();
                        document.getElementById('result').innerHTML = 'File converted successfully! Download started.';
                    } else {
                        const error = await response.text();
                        document.getElementById('result').innerHTML = 'Error: ' + error;
                    }
                } catch (error) {
                    console.error('Upload error:', error);
                    document.getElementById('result').innerHTML = 'Error: ' + error.message;
                }
            };
        </script>
    </body>
    </html>
    """

@app.post("/upload/")
async def upload_file(file: UploadFile = File(...)):
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
    temp_pdf_path = None
    temp_xlsx_path = None
    
    try:
        # Save uploaded PDF
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_pdf:
            content = await file.read()
            temp_pdf.write(content)
            temp_pdf_path = temp_pdf.name
        
        # Extract text
        text_content = extract_text_from_pdf(temp_pdf_path)
        if not text_content.strip():
            raise HTTPException(status_code=400, detail="No text extracted from PDF")
        
        # Parse claims
        claims_data = parse_insurance_claims(text_content)
        if not claims_data:
            # Save debug file
            with open('debug_text.txt', 'w', encoding='utf-8') as f:
                f.write(text_content)
            raise HTTPException(status_code=400, detail="No claims data found in PDF")
        
        # Create Excel file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_xlsx:
            temp_xlsx_path = temp_xlsx.name
        
        create_xlsx_file(claims_data, temp_xlsx_path)
        
        # Don't delete temp file in finally block, let FileResponse handle it
        return FileResponse(
            temp_xlsx_path,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            filename='insurance_claims.xlsx'
        )
    
    except Exception as e:
        # Clean up on error
        if temp_pdf_path and os.path.exists(temp_pdf_path):
            os.unlink(temp_pdf_path)
        if temp_xlsx_path and os.path.exists(temp_xlsx_path):
            os.unlink(temp_xlsx_path)
        raise HTTPException(status_code=500, detail=str(e))
    
    finally:
        # Only clean up PDF file
        if temp_pdf_path and os.path.exists(temp_pdf_path):
            os.unlink(temp_pdf_path)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)